import {
  BookOpen,
  Command,
  Frame,
  LifeBuoy,
  Map,
  PieChart,
  Settings,
  SquareTerminal,
  User2,
} from 'lucide-react'

import { NavMain } from '@/components/nav-main'
import { NavProjects } from '@/components/nav-projects'
import { NavSecondary } from '@/components/nav-secondary'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'

const data = {
  navMain: [
    {
      title: 'Playground',
      url: '/',
      icon: SquareTerminal,
      isActive: true,
    },
    {
      title: 'Documentación',
      url: '/',
      icon: BookOpen,
    },
    {
      title: 'Configuración',
      url: '/settings',
      icon: Settings,
      items: [
        {
          title: 'General',
          url: '/',
        },
        {
          title: 'Team',
          url: '/',
        },
        {
          title: 'Billing',
          url: '/',
        },
        {
          title: 'Limits',
          url: '/',
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: 'Support',
      url: '/',
      icon: LifeBuoy,
    },
    {
      title: 'Sign In',
      url: '/login',
      action: 'login-with-redirect',
      icon: User2,
    },
  ],
  projects: [
    {
      name: 'Design Engineering',
      url: '/',
      icon: Frame,
    },
    {
      name: 'Sales & Marketing',
      url: '/',
      icon: PieChart,
    },
    {
      name: 'Travel',
      url: '/',
      icon: Map,
    },
  ],
}

export function AppSidebar({ ...props }) {
  const { children } = props

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="#">
                <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                  <Command className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">Acme Inc</span>
                  <span className="truncate text-xs">Enterprise</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.projects} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>{children}</SidebarFooter>
    </Sidebar>
  )
}
