import { Link } from 'react-router-dom'

export default function Component() {
  return (
    <div className="flex items-center min-h-screen px-4 py-12 sm:px-6 md:px-8 lg:px-12 xl:px-16">
      <div className="w-full space-y-6 text-center">
        <div className="space-y-3">
          <h1 className="text-4xl font-bold tracking-tighter sm:text-8xl">
            404
          </h1>
          <p className="text-gray-500">
            Lo sentimos, no pudimos encontrar la página que buscas.
          </p>
        </div>
        <Link
          to="/"
          className="inline-flex h-10 items-center rounded-md border border-gray-200 border-gray-200 bg-white shadow-sm px-8 text-sm font-medium transition-colors hover:bg-gray-100 hover:text-gray-900"
        >
          Regresar al inicio
        </Link>
      </div>
    </div>
  )
}
