import { useAuth0 } from '@auth0/auth0-react'
import { Outlet } from 'react-router-dom'

import { AppSidebar } from '@/components/app-sidebar'
import { NavUser } from '@/components/nav-user'
import { SiteHeader } from '@/components/site-header'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'

const emptyUser = {
  name: 'Guest',
  email: 'Usuario no autenticado',
  avatar: null,
}

export function Basic() {
  const { user, isAuthenticated } = useAuth0()

  return (
    <SidebarProvider>
      <AppSidebar>
        <NavUser user={isAuthenticated ? user : emptyUser} />
      </AppSidebar>
      <SidebarInset>
        <SiteHeader>Configuración</SiteHeader>
        <Outlet />
      </SidebarInset>
    </SidebarProvider>
  )
}
