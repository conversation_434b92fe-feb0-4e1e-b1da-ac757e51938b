import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { useAuth0 } from '@auth0/auth0-react'

export function StarterCard(props) {
  const { children, title, description } = props
  const { loginWithRedirect } = useAuth0()

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>

      <CardContent>
        {children}

        <div className="mt-4 text-center text-sm">
          ¿Tienes una cuenta? <span> </span>
          <a
            onClick={() => loginWithRedirect()}
            className="underline underline-offset-4 cursor-pointer"
          >
            Acceder
          </a>
        </div>
      </CardContent>
    </Card>
  )
}
