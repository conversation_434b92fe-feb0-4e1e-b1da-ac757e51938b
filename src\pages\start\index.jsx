import { useRequest } from '@/fetcher'
import { useSearchParams } from 'react-router-dom'

import { StarterCard } from './components/starter-card'
import { StarterForm } from './components/starter-form'

const QueryParams = {
  CLINIC: 'q',
}

export default function Start() {
  const [searchParams] = useSearchParams()
  const slug = searchParams.get(QueryParams.CLINIC)
  const { data: clinic } = useRequest(() => slug && `/clinics/${slug}`)

  const clinicName = clinic && clinic.name
  const clinicId = clinic && clinic.id

  return (
    <StarterCard
      title={clinicName}
      description="Registra tus datos para la atención"
    >
      <StarterForm clinicId={clinicId} />
    </StarterCard>
  )
}
