{"name": "vet", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@auth0/auth0-react": "^2.3.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.33.0", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "lucide-react": "^0.511.0", "next-themes": "^0.4.6", "react": "19.1.0", "react-day-picker": "^9.7.0", "react-dom": "19.1.0", "react-hook-form": "^7.56.4", "react-router-dom": "7.6.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "vaul": "^1.1.2", "zod": "^3.25.28"}, "devDependencies": {"@eslint/js": "9.25.0", "@types/node": "^22.15.18", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "@vitejs/plugin-react-swc": "3.9.0", "eslint": "9.25.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.19", "globals": "16.0.0", "msw": "^2.8.6", "tw-animate-css": "^1.3.0", "vite": "6.3.5"}, "msw": {"workerDirectory": [""]}}