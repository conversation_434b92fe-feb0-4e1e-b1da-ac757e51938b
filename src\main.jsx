import { Toaster } from '@/components/ui/sonner'
import { authentication } from '@/configuration'
import { Fetcher } from '@/fetcher'
import { worker } from '@/mocks/browser'
import { routes } from '@/routes'
import { Auth0Provider } from '@auth0/auth0-react'
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { RouterProvider, createBrowserRouter } from 'react-router-dom'

import './main.css'

const router = createBrowserRouter(routes)
const redirectUri = window.location.origin
const authorizationParams = { redirect_uri: redirectUri }

if (import.meta.env.VITE_MOCK_ENABLED) {
  await worker.start({
    onUnhandledRequest: 'bypass',
    quiet: true,
  })
}

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <Auth0Provider
      domain={authentication.domain}
      clientId={authentication.clientId}
      authorizationParams={authorizationParams}
    >
      <Fetcher>
        <RouterProvider router={router} />
        <Toaster />
      </Fetcher>
    </Auth0Provider>
  </StrictMode>,
)
