import { serverlessUrl } from '@/configuration'
import useSWR, { SWRConfig } from 'swr'

export function useRequest(key) {
  return useSWR(key)
}

export function Fetcher(props) {
  const { children } = props

  async function fetcher(resource) {
    const url = new URL(resource, serverlessUrl)
    const res = await fetch(url)
    return await res.json()
  }

  return SWRConfig({ children, value: { fetcher } })
}
