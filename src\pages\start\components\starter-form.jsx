import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { InputSelect } from '@/components/ui/input-select'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

const schema = z.object({
  identifier: z.string().length(8).regex(/^\d+$/),
  reason: z.string(),
})

export function StarterForm(props) {
  const defaultValues = { identifier: '', reason: '1' }
  const form = useForm({ resolver: zodResolver(schema), defaultValues })

  const { clinicId } = props

  function onSubmit(values) {
    toast(JSON.stringify(values))
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-6"
      >
        <FormField
          control={form.control}
          name="identifier"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Documento de Identidad</FormLabel>
              <FormControl>
                <Input {...field} placeholder="Ingresa tu número de DNI" />
              </FormControl>
              <FormDescription>
                Este es tu identificador dentro de la plataforma
              </FormDescription>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="reason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Motivo de atención</FormLabel>
              <InputSelect
                url={clinicId && `/clinics/${clinicId}/reasons`}
                field={field}
                placeholder="Selecciona un motivo"
                idKey="id"
                displayKey="name"
                descriptionKey="description"
              />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full">
          Continuar
        </Button>
      </form>
    </Form>
  )
}
