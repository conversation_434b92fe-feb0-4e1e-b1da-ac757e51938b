import { useRequest } from '@/fetcher'
import { FormControl, FormDescription } from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'

export function InputSelect({
  url,
  field,
  disabled: externalDisabled = false,
  idKey = 'id',
  displayKey = 'name',
  descriptionKey = 'description',
  placeholder = 'Selecciona una opción',
  ...props
}) {
  const { data, isLoading, error } = useRequest(() => url)
  
  const isDisabled = externalDisabled || isLoading || !data || error
  const selectedItem = data?.find((item) => item[idKey] == field.value)

  if (isLoading) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <Skeleton className="h-9 w-full rounded-md" />
        </div>
        <Skeleton className="h-4 w-3/4" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="h-9 w-full rounded-md border border-destructive bg-destructive/10 flex items-center px-3 text-sm text-destructive">
            Error al cargar opciones
          </div>
        </div>
        <p className="text-sm text-destructive">
          No se pudieron cargar las opciones. Intenta nuevamente.
        </p>
      </div>
    )
  }

  return (
    <>
      <Select
        onValueChange={field.onChange}
        defaultValue={field.value}
        disabled={isDisabled}
        {...props}
      >
        <FormControl>
          <SelectTrigger className="w-full">
            <SelectValue placeholder={placeholder} />
          </SelectTrigger>
        </FormControl>
        <SelectContent>
          {data?.map((item) => (
            <SelectItem key={item[idKey]} value={`${item[idKey]}`}>
              {item[displayKey]}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {selectedItem && selectedItem[descriptionKey] && (
        <FormDescription>
          {selectedItem[descriptionKey]}
        </FormDescription>
      )}
    </>
  )
}
