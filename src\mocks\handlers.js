import { serverlessUrl } from '@/configuration'
import { delay, http, HttpResponse } from 'msw'

export const handlers = [
  http.get(`${serverlessUrl}/clinics/:slug`, async (request) => {
    const { slug } = request.params
    await delay('real')
    return HttpResponse.json({
      id: 1,
      name: 'Veterinaria de prueba',
      slug: slug,
    })
  }),
  http.get(`${serverlessUrl}/clinics/1/reasons`, async () => {
    await delay('real')
    return HttpResponse.json([
      {
        id: 1,
        name: 'Consulta general',
        description: 'Detalles de la consulta general',
      },
      {
        id: 2,
        name: 'Control de vacunación',
        description: 'Detalles del control de vacunación',
      },
      {
        id: 3,
        name: 'Consulta preventiva',
        description: 'Detalles de la consulta preventiva',
      },
      {
        id: 4,
        name: 'Servicio de limpieza',
        description: 'Detalles del servicio de limpieza',
      },
    ])
  }),
]
